import { BaseService } from '../../BaseService/index'
import { page, remove } from '@/api/admin/base'

export class DataService extends BaseService {
  constructor(context) {
    super(context)
    this.queryParams = {}
  }

  // 更新查询参数
  updateQueryParams(externalParams = {}) {
    const params = {}

    // 处理表单数据（如果存在）
    if (this.context.formData) {
      for (let key in this.context.formData) {
        if (typeof this.context.formData[key] === 'object') {
          for (let subKey in this.context.formData[key]) {
            if (this.context.formData[key][subKey]) {
              params[`${key}.${subKey}`] = this.context.formData[key][subKey]
            }
          }
        } else {
          if (this.context.formData[key]) {
            params[key] = this.context.formData[key]
          }
        }
      }
    }

    this.queryParams = {
      ...params,
      ...externalParams, // 合并外部传入的参数
      current: this.context.gridOptions.pagerConfig?.currentPage || 1,
      size: this.context.gridOptions.pagerConfig?.pageSize || 500
    }
  }

  // 处理查询
  async handleQuery() {
    if (typeof this.context.defineQuery === 'function') {
      await this.context.defineQuery()
      return
    }

    this.setLoading(true)
    try {
      const{current,size, ...searchInfo } = this.queryParams
      const data = {
        FUNID: this.context.FUNID,
        id: "1948669183848050688",
        current,
        size,
        data: Object.values(searchInfo)
      }
      
      const modifiedData = await this.emitSyncEvent('before-query', data)
      if (!modifiedData) return
      
      const result = await page(modifiedData, this.context.MODULEValue)
      
      if (this.context.$refs.gridRef) {
        this.context.$refs.gridRef.loadData(result.data.records)
        this.context.gridOptions.pagerConfig.total = result.data.total

        // 数据加载完成后更新列筛选选项和应用排序
        this.context.$nextTick(() => {
          this.updateColumnFilters()
          // 应用排序规则
          this.context.applySorting()
        })
      }
      
      await this.emitSyncEvent('after-query', result)
      return result
    } catch (error) {
      this.requestFailed(error)
    } finally {
      this.setLoading(false)
    }
  }

  // 处理创建
  async handleCreate() {
    if (typeof this.context.defineCreate === 'function') {
      await this.context.defineCreate()
      return
    }

    if (this.context.detailConfig.routerName) {
      this.navigateToDetail(null)
      return
    }

    const modifiedData = await this.emitSyncEvent('before-create', {})
    if (!modifiedData) return
  }

  // 处理编辑
  async handleEdit(params = {}) {
    let record = params.row
    if (!record) {
      const selectedRecords = this.getSelectedRows()
      if (!selectedRecords || selectedRecords.length === 0) {
        this.context.$message.warning(this.context.$t('public.list'))
        return
      }
      record = selectedRecords[0]
    }

    if (typeof this.context.defineEdit === 'function') {
      await this.context.defineEdit(record)
      return
    }

    if (this.context.detailConfig.routerName) {
      this.navigateToDetail(record[this.context.detailConfig.idField || this.context.idField])
      return
    }

    const modifiedRecord = await this.emitSyncEvent('before-edit', { row: record })
    if (!modifiedRecord) return
  }

  // 处理删除
  async handleRemove() {
    if (typeof this.context.defineRemove === 'function') {
      await this.context.defineRemove()
      return
    }

    const selectedRecords = this.getSelectedRows()
    if (!selectedRecords || selectedRecords.length === 0) {
      this.context.$message.warning(this.context.$t('public.list'))
      return
    }

    const records = selectedRecords.map(record => ({
      [this.context.idField || 'id']: record[this.context.idField || 'id']
    }))

    const modifiedRecords = await this.emitSyncEvent('before-remove', records)
    if (modifiedRecords && modifiedRecords.length > 0) {
      try {
        await this.context.$confirm(
          this.context.$t('public.del.content'),
          this.context.$t('public.del.title'),
          {
            confirmButtonText: this.context.$t('public.sure'),
            cancelButtonText: this.context.$t('public.cancel'),
            type: 'warning'
          }
        )

        this.setLoading(true)
        const data = {
          FUNID: this.context.FUNID,
          DATA: [...modifiedRecords],
        }
        const result = await remove(data, this.context.MODULEValue)
        this.showSuccess()
        await this.handleQuery()
        await this.emitSyncEvent('after-remove', result)
      } catch (error) {
        this.requestFailed(error)
      } finally {
        this.setLoading(false)
      }
    }
  }

  // 获取选中的行记录
  getSelectedRows() {
    if (!this.context.$refs.gridRef) {
      return []
    }
    if (this.context.multiple) {
      return this.context.$refs.gridRef.getCheckboxRecords() || []
    } else {
      const radioRecord = this.context.$refs.gridRef.getRadioRecord()
      return radioRecord ? [radioRecord] : []
    }
  }

  // 获取列的唯一值作为筛选选项
  getColumnUniqueValues(field) {
    const data = this.context.$refs.gridRef.getTableData().fullData
    if (!data || data.length === 0) {
      return []
    }
    
    const uniqueValues = [...new Set(
      data.map(row => row[field])
        .filter(value => value !== null && value !== undefined && value !== '')
    )].sort()
    
    return uniqueValues.map(value => ({
      label: String(value),
      value: value
    }))
  }

  // 处理筛选变化
  handleFilterChange({ column, property, values }) {
    console.log('Filter changed:', { column, property, values })
    
    if (!values || values.length === 0) {
      this.clearColumnFilter(property)
      return
    }
    
    this.applyColumnFilter(property, values)
  }

  // 应用列筛选
  applyColumnFilter(field, selectedValues) {
    if (!this.context.$refs.gridRef) return
    
    this.context.$refs.gridRef.setFilter(field, selectedValues)
  }

  // 清除指定列的筛选
  clearColumnFilter(field) {
    if (!this.context.$refs.gridRef) return
    
    this.context.$refs.gridRef.clearFilter(field)
  }

  // 清除所有筛选
  clearAllFilters() {
    if (this.context.$refs.gridRef) {
      this.context.$refs.gridRef.clearFilter()
    }
  }

  // 获取当前筛选条件
  getCurrentFilters() {
    if (this.context.$refs.gridRef) {
      return this.context.$refs.gridRef.getCheckedFilters()
    }
    return []
  }

  // 更新列筛选选项
  updateColumnFilters() {
    if (!this.context.$refs.gridRef || this.context.$refs.gridRef.getTableData().fullData.length === 0) {
      return
    }
    
    const columns = this.context.$refs.gridRef.getColumns()
    
    columns.forEach(column => {
      if (column.field && column.filters !== undefined) {
        const uniqueValues = this.getColumnUniqueValues(column.field)
        if (uniqueValues.length > 0) {
          this.context.$refs.gridRef.setFilter(column, uniqueValues)
        }
      }
    })
  }

  // 导航到详情页
  navigateToDetail(id) {
    if (!this.context.detailConfig.routerName) return
    
    const data = this.context.$refs.gridRef.getTableData().tableData
    const ids = data.map(r => r[this.context.idField])
    const index = ids.indexOf(id)
    console.log('navigateList', ids)
    console.log('navigateItem', id)
    
    // 主子表专属导航功能
    this.context.$store.commit('toolbarPage/addItem', { 
      routerName: this.context.detailConfig.routerName, 
      data: ids, 
      index 
    })
    
    this.context.$router.push({ 
      name: this.context.detailConfig.routerName, 
      params: { id } 
    })
  }

  // 填充表单数据
  fillFormData(data) {
    Object.keys(this.context.formData).forEach(key => {
      this.context.$set(this.context.formData, key, null)
    })
    
    Object.keys(data).forEach(key => {
      this.context.$set(this.context.formData, key, data[key])
    })
  }

  // 重置表单
  handleResetForm() {
    this.context.recordId = null
    const formData = this.context.formData
    for (const key in formData) {
      if (formData.hasOwnProperty(key)) {
        formData[key] = null
      }
    }
  }
} 