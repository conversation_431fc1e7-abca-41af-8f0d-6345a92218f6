/*
* 负责基础的表格实现
* 提供表格的工具栏、表单、表格、打印等组件
* 提供表格的查询、创建、编辑、删除按钮事件
**/
<template>
  <div class="table-container">
    <header v-if="showToolbar || showForm">
      <!-- 工具栏 -->
      <vToolbar
        v-if="showToolbar"
        ref="toolbarRef"
        :data="computedToolbarItems"
        @toolbarClick="handleToolbarClick">
      </vToolbar>


      <!-- 搜索表单组件 -->
      <SearchForm
        ref="searchFormRef"
        :field-config-extensions="searchConfig"
        :max-conditions="maxConditions"
        :FUNID="FUNID"
        :MODULE="MODULE"
        @search="handleSearchFormSearch"
        @selectListEvent="handleSelectListEvent"
        @iconClick="handleCompEvent"
        @initialized="handleSearchFormInitialized"
        @schemeChange="handleSchemeChange"
      />
    </header>

    <!-- 主体内容 -->
    <main>
        <vxe-grid
        ref="gridRef"
        class="vxe-grid-common"
        v-bind="gridOptions"
        @checkbox-change="$emit('checkbox-change', $event)"
        @radio-change="$emit('radio-change', $event)"
        @cell-dblclick="handleCellDblclick"
        @page-change="$emit('page-change', $event)">
      </vxe-grid>
    </main>

    <!-- 打印组件 -->
    <component 
      :is="guestComponent(printType)" 
      :data="printType" 
      ref="printDialog" />
  </div>
</template>

<script>
import Forms from '@/components/Forms/index.vue'
import vToolbar from '@/components/amtxts/common/Toolbar/BaseToolbar.vue'
import PrintDialog from '@/components/printDialog/index.vue'
import ReportPrintDialog from '@/components/printDialog/ReportPrintDialog.vue'
import VxeGrid from 'vxe-table/lib/grid'
import PrintManager from '@/components/amtxts/utils/printManager'
import DataMixin from './mixins/DataMixin'
import GridMixin from './mixins/GridMixin'
import SearchForm from '@/components/amtxts/common/SearchForm/index.vue'

export default {
  name: 'vGrid',
  
  mixins: [DataMixin, GridMixin],
  
  components: {
    SearchForm,
    Forms,
    vToolbar,
    PrintDialog,
    ReportPrintDialog,
    VxeGrid,
  },

  props: {
    // 打印类型
    printType: {
      type: Number,
      default: 0,
      required: false
    },
    // 工具栏配置
    toolbarItems: {
      type: Array,
      default: () => []
    },
    // 表格列配置
    tableColumn: {
      type: Array,
      default: () => []
    },
    // 功能ID
    FUNID: {
      type: String,
      required: true
    },
    // ID字段
    idField: {
      type: String,
      default: 'id'
    },
    // 列最小宽度
    columnMinWidth: {
      type: String,
      default: '120px'
    },
    // 是否显示序号列
    showSeq: {
      type: Boolean,
      default: false
    },
    // 是否显示选择列
    showCheckbox: {
      type: Boolean,
      default: true
    },
    // 是否允许多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 是否显示工具栏
    showToolbar: {
      type: Boolean,
      default: true
    },
    // 是否显示表单
    showForm: {
      type: Boolean,
      default: true
    },
    // SearchForm 相关配置
    searchConfig: {
      type: Array,
      default: () => []
    },
    maxConditions: {
      type: Number,
      default: 12
    }
  },

  data() {
    return {
      formData: {},
      printManager: null
    }
  },

  computed: {
    // 计算最终的工具栏配置
    computedToolbarItems() {
      return [
        { label: 'toolbar.scheme', value: 'scheme', icon: 'icon-design', visible: true },
        { label: 'toolbar.query', value: 'query', icon: 'icon-query', visible: false},
        ...this.toolbarItems,
      ]
    }
  },

  async mounted() {
    console.log('MODULE', this.MODULEValue)

    // 初始化打印管理器
    this.printManager = new PrintManager({
      printType: this.printType,
      path: this.$route.path,
      gridData: this.$refs.gridRef.getTableData().fullData,
      gridRef: this.$refs.gridRef,
      printDialogRef: this.$refs.printDialog,
      $message: this.$message,
      $t: this.$t
    });
  },

  beforeDestroy() {
    // 清理资源
    this.clearSelection()
    this.printManager = null
  },

  methods: {
    // 工具栏点击处理
    handleToolbarClick(params) {
      switch (params) {
        case 'create':
          this.handleCreate()
          break
        case 'query':
          this.handleQuery()
          break
        case 'edit':
          this.handleEdit()
          break
        case 'remove':
          this.handleRemove()
          break
        case 'print':
          this.handlePrint()
          break
        case 'export':
          this.handleExport()
          break
        case 'scheme':
          this.handleScheme()
          break
        default:
          this.$emit('toolbar-click', { code: params })
      }
    },
    
    handleCompEvent(params) {
      this.$emit('form-item-icon-click', params)
    },
    
    handleScheme(){
      this.$refs.searchFormRef.$refs.schemeDialogRef.handleShow()
    },
    handleCellDblclick(params) {
      this.dataService.emitSyncEvent('before-edit', params)
      this.handleEdit({row: params.row, column: params.column})
    },
    
    handleSelectListEvent(event, fillMap) {
      switch (event.code) {
        case "choose":
          const data = event.data.filterRecord || event.data.rawRecord
          Object.keys(data).forEach(key => {
            this.$set(this.formData, key, data[key])
          })
          break
      }
      this.$emit('select-list-event', event)
    },
    
    guestComponent(type) {
      switch (type) {
        case 0:
          return PrintDialog
        default:
          return ReportPrintDialog
      }
    },
    


    // 清除选择
    clearSelection() {
      if (!this.$refs.gridRef) return
      this.multiple
        ? this.$refs.gridRef.clearCheckboxRow()
        : this.$refs.gridRef.clearRadioRow()
    },

    // 刷新表格数据
    refreshTable() {
      this.handleQuery()
    },

    // 重置所有
    reset() {
      this.handleResetForm()
      this.clearSelection()
      this.refreshTable()
    },

    // 处理导出
    async handleExport() {
      this.$refs.gridRef.exportData({ type: 'csv' })
    },

    // 处理搜索表单初始化完成事件
    handleSearchFormInitialized(schemeData) {
      // 接收SearchForm传递的方案数据
      if (schemeData) {
        this.columnContent = schemeData.columnContent
        this.sortContent = schemeData.sortContent
        this.currentView = schemeData.currentView // 存储当前方案信息

        console.log('接收到SearchForm的方案数据:', schemeData)

        // 更新表格列配置
        this.updateGridColumns()
      }

      const queryParams = this.$refs.searchFormRef.getSearchParams()
      this.handleSearchFormSearch(queryParams)
    },

    // 处理搜索表单的搜索事件
    handleSearchFormSearch(searchParams) {
      const queryParams = searchParams.map(item => {
        return {
          field: item.field,
          operator: item.operator,
          value: item.value,
          groupid: item.groupid
        }
      })
      // 更新查询参数并执行查询
      this.updateQueryParams(queryParams)
      this.handleQuery()

      // 触发搜索事件
      this.$emit('search', searchParams)
    },

    // 处理方案切换事件
    handleSchemeChange(schemeData) {
      // 更新列配置数据
      this.columnContent = schemeData.columnContent
      this.sortContent = schemeData.sortContent
      this.currentView = schemeData.currentView // 更新当前方案信息
      // 更新表格列配置
      this.updateGridColumns()

      // 在下一个tick中应用排序（确保列配置已更新）
      this.$nextTick(() => {
        this.applySorting()
      })
    },
  }
}
</script>

<style scoped lang="less">
.table-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  header {
    flex-shrink: 0;
  }

  main {
    flex: 1;
    height: 0; // 确保flex布局正常工作
  }
}

// 确保vxe-grid使用通用样式
.vxe-grid-common {
  height: 100%;
}
</style>
